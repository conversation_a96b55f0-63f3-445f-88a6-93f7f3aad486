using System;
using System.Windows;
using System.Windows.Controls;
using FluentSystemDesign.Examples.Pages;
using FluentSystemDesign.Examples.Pages.Examples;

namespace FluentSystemDesign.Examples
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private bool _isDarkTheme = false;

        public MainWindow()
        {
            InitializeComponent();

            // 延迟导航到首页，避免初始化问题
            this.Loaded += (s, e) => NavigateToHomePage();
        }

        #region 导航方法

        /// <summary>
        /// 导航到首页
        /// </summary>
        private void NavigateToHomePage()
        {
            var homePage = new HomePage();
            ContentFrame.Navigate(homePage);
            UpdateStatus("首页");
        }

        /// <summary>
        /// 导航到行为类示例页面
        /// </summary>
        public void NavigateToBehaviorsPage()
        {
            var behaviorsPage = new BehaviorsDemoPage();
            ContentFrame.Navigate(behaviorsPage);
            UpdateStatus("行为类示例");
        }

        /// <summary>
        /// 导航到值转换器示例页面
        /// </summary>
        public void NavigateToValueConvertersPage()
        {
            var valueConvertersPage = new ValueConvertersDemoPage();
            ContentFrame.Navigate(valueConvertersPage);
            UpdateStatus("值转换器示例");
        }

        /// <summary>
        /// 导航到视觉效果示例页面
        /// </summary>
        public void NavigateToEffectsPage()
        {
            var effectsPage = new EffectsDemoPageSimple();
            ContentFrame.Navigate(effectsPage);
            UpdateStatus("视觉效果示例");
        }

        /// <summary>
        /// 更新状态栏文本
        /// </summary>
        /// <param name="status">状态文本</param>
        private void UpdateStatus(string status)
        {
            StatusText.Text = $"当前页面: {status}";
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 搜索框文本改变事件
        /// </summary>
        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = SearchBox.Text;
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                UpdateStatus($"搜索: {searchText}");
                // TODO: 实现搜索功能
            }
            else
            {
                UpdateStatus("就绪");
            }
        }

        /// <summary>
        /// 主题切换按钮点击事件
        /// </summary>
        private void ThemeToggle_Click(object sender, RoutedEventArgs e)
        {
            _isDarkTheme = !_isDarkTheme;
            var button = sender as Button;
            if (button != null)
            {
                button.Content = _isDarkTheme ? "☀️" : "🌙";
                button.ToolTip = _isDarkTheme ? "切换到浅色主题" : "切换到深色主题";
            }

            // TODO: 实现主题切换逻辑
            UpdateStatus(_isDarkTheme ? "已切换到深色主题" : "已切换到浅色主题");
        }

        /// <summary>
        /// 设置按钮点击事件
        /// </summary>
        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("设置功能将在后续版本中实现", "设置", MessageBoxButton.OK, MessageBoxImage.Information);
            UpdateStatus("设置");
        }

        /// <summary>
        /// 关于按钮点击事件
        /// </summary>
        private void About_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("FluentSystemDesign WPF控件库\n版本 1.0.0\n\n基于Fluent Design System设计语言的WPF控件库",
                          "关于", MessageBoxButton.OK, MessageBoxImage.Information);
            UpdateStatus("关于");
        }

        #endregion

        #region 导航按钮事件

        /// <summary>
        /// 导航到首页
        /// </summary>
        private void NavigateToHome_Click(object sender, RoutedEventArgs e)
        {
            NavigateToHomePage();
        }

        /// <summary>
        /// 导航到行为类示例
        /// </summary>
        private void NavigateToBehaviors_Click(object sender, RoutedEventArgs e)
        {
            NavigateToBehaviorsPage();
        }

        /// <summary>
        /// 导航到值转换器示例
        /// </summary>
        private void NavigateToValueConverters_Click(object sender, RoutedEventArgs e)
        {
            NavigateToValueConvertersPage();
        }

        /// <summary>
        /// 导航到基础控件示例
        /// </summary>
        private void NavigateToBasicControls_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("基础控件示例将在后续版本中实现", "基础控件", MessageBoxButton.OK, MessageBoxImage.Information);
            UpdateStatus("基础控件");
        }

        /// <summary>
        /// 导航到输入控件示例
        /// </summary>
        private void NavigateToInputControls_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("输入控件示例将在后续版本中实现", "输入控件", MessageBoxButton.OK, MessageBoxImage.Information);
            UpdateStatus("输入控件");
        }

        /// <summary>
        /// 导航到导航控件示例
        /// </summary>
        private void NavigateToNavigationControls_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("导航控件示例将在后续版本中实现", "导航控件", MessageBoxButton.OK, MessageBoxImage.Information);
            UpdateStatus("导航控件");
        }

        /// <summary>
        /// 导航到布局控件示例
        /// </summary>
        private void NavigateToLayoutControls_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("布局控件示例将在后续版本中实现", "布局控件", MessageBoxButton.OK, MessageBoxImage.Information);
            UpdateStatus("布局控件");
        }

        /// <summary>
        /// 导航到色彩系统
        /// </summary>
        private void NavigateToColors_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("色彩系统将在后续版本中实现", "色彩系统", MessageBoxButton.OK, MessageBoxImage.Information);
            UpdateStatus("色彩系统");
        }

        /// <summary>
        /// 导航到字体排版
        /// </summary>
        private void NavigateToTypography_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("字体排版将在后续版本中实现", "字体排版", MessageBoxButton.OK, MessageBoxImage.Information);
            UpdateStatus("字体排版");
        }

        /// <summary>
        /// 导航到视觉效果示例
        /// </summary>
        private void NavigateToEffects_Click(object sender, RoutedEventArgs e)
        {
            NavigateToEffectsPage();
        }

        /// <summary>
        /// 导航到快速开始
        /// </summary>
        private void NavigateToGettingStarted_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("快速开始指南将在后续版本中实现", "快速开始", MessageBoxButton.OK, MessageBoxImage.Information);
            UpdateStatus("快速开始");
        }

        /// <summary>
        /// 导航到完整文档
        /// </summary>
        private void NavigateToDocumentation_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("完整文档将在后续版本中实现", "完整文档", MessageBoxButton.OK, MessageBoxImage.Information);
            UpdateStatus("完整文档");
        }

        #endregion
    }
}
