﻿#pragma checksum "..\..\..\..\..\Pages\Examples\ValueConvertersDemoPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "681DB232D6D9B8FE95BEF4D0F922981FF27359EF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FluentSystemDesign.WPF.Converters;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FluentSystemDesign.Examples.Pages.Examples {
    
    
    /// <summary>
    /// ValueConvertersDemoPage
    /// </summary>
    public partial class ValueConvertersDemoPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 40 "..\..\..\..\..\Pages\Examples\ValueConvertersDemoPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BoolTestCheckBox;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\..\Pages\Examples\ValueConvertersDemoPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider NumberSlider;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Pages\Examples\ValueConvertersDemoPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HexColorTextBox;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Pages\Examples\ValueConvertersDemoPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StringTestTextBox;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\..\Pages\Examples\ValueConvertersDemoPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider CountSlider;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri(("/FluentSystemDesign.Examples;V1.0.0.0;component/pages/examples/valueconvertersdem" +
                    "opage.xaml"), System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Pages\Examples\ValueConvertersDemoPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BoolTestCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 2:
            this.NumberSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 3:
            this.HexColorTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.StringTestTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.CountSlider = ((System.Windows.Controls.Slider)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

