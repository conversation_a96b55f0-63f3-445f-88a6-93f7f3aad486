<Page x:Class="FluentSystemDesign.Examples.Pages.Examples.EffectsDemoPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:effects="clr-namespace:FluentSystemDesign.WPF.Effects.Helpers;assembly=FluentSystemDesign.WPF"
      Title="视觉效果演示"
      Background="{StaticResource BackgroundBrush}">

    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
        <StackPanel>
            
            <!-- 页面标题 -->
            <TextBlock Text="视觉效果系统演示" 
                       Style="{StaticResource HeadingTextStyle}"
                       Margin="0,0,0,20"/>

            <!-- 阴影效果演示 -->
            <GroupBox Header="阴影效果 (Elevation System)"
                      Padding="20"
                      Margin="0,0,0,30"
                      Background="{StaticResource SurfaceBrush}">
                <StackPanel>
                    
                    <!-- 基础阴影级别 -->
                    <TextBlock Text="基础阴影级别 (Elevation 0-24)" 
                               Style="{StaticResource SubheadingTextStyle}"/>
                    
                    <WrapPanel Orientation="Horizontal" 
                               HorizontalAlignment="Left">
                        
                        <!-- Elevation 0 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource Elevation0}">
                            <TextBlock Text="0" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- Elevation 1 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource Elevation1}">
                            <TextBlock Text="1" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- Elevation 2 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource Elevation2}">
                            <TextBlock Text="2" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- Elevation 4 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource Elevation4}">
                            <TextBlock Text="4" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- Elevation 8 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource Elevation8}">
                            <TextBlock Text="8" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- Elevation 12 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource Elevation12}">
                            <TextBlock Text="12" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- Elevation 24 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource Elevation24}">
                            <TextBlock Text="24" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       FontWeight="Bold"/>
                        </Border>
                    </WrapPanel>

                    <!-- 专用阴影效果 -->
                    <TextBlock Text="专用阴影效果" 
                               Style="{StaticResource SubheadingTextStyle}"
                               Margin="0,20,0,0"/>
                    
                    <WrapPanel Orientation="Horizontal">
                        
                        <!-- 卡片阴影 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="8"
                                Effect="{StaticResource CardShadow}">
                            <TextBlock Text="Card Shadow" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 按钮阴影 -->
                        <Button Width="120" Height="40" Margin="10"
                                Content="Button Shadow"
                                Effect="{StaticResource ButtonShadow}"/>

                        <!-- 菜单阴影 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource MenuBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource MenuShadow}">
                            <TextBlock Text="Menu Shadow" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 浮动阴影 -->
                        <Border Width="60" Height="60" Margin="10"
                                Background="{StaticResource AccentBrush}"
                                CornerRadius="30"
                                Effect="{StaticResource FloatingShadow}">
                            <TextBlock Text="FAB" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>
                    </WrapPanel>
                </StackPanel>
            </GroupBox>

            <!-- 发光效果演示 -->
            <GroupBox Header="发光效果 (Glow Effects)"
                      Padding="20"
                      Margin="0,0,0,30"
                      Background="{StaticResource SurfaceBrush}">
                <StackPanel>
                    
                    <!-- 基础发光强度 -->
                    <TextBlock Text="发光强度级别" 
                               Style="{StaticResource SubheadingTextStyle}"/>
                    
                    <WrapPanel Orientation="Horizontal">
                        
                        <!-- 轻微发光 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource SubtleGlow}">
                            <TextBlock Text="Subtle" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 轻度发光 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource LightGlow}">
                            <TextBlock Text="Light" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 中度发光 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource MediumGlow}">
                            <TextBlock Text="Medium" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 强度发光 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource StrongGlow}">
                            <TextBlock Text="Strong" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>
                    </WrapPanel>

                    <!-- 主题色发光 -->
                    <TextBlock Text="主题色发光" 
                               Style="{StaticResource SubheadingTextStyle}"
                               Margin="0,20,0,0"/>
                    
                    <WrapPanel Orientation="Horizontal">
                        
                        <!-- 主色发光 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource PrimaryGlow}">
                            <TextBlock Text="Primary" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 次要色发光 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource SecondaryGlow}">
                            <TextBlock Text="Secondary" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 强调色发光 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource AccentGlow}">
                            <TextBlock Text="Accent" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>
                    </WrapPanel>

                    <!-- 语义发光 -->
                    <TextBlock Text="语义状态发光" 
                               Style="{StaticResource SubheadingTextStyle}"
                               Margin="0,20,0,0"/>
                    
                    <WrapPanel Orientation="Horizontal">
                        
                        <!-- 成功发光 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource SuccessGlow}">
                            <TextBlock Text="Success" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 警告发光 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource WarningGlow}">
                            <TextBlock Text="Warning" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 错误发光 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource ErrorGlow}">
                            <TextBlock Text="Error" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 信息发光 -->
                        <Border Width="80" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                Effect="{StaticResource InfoGlow}">
                            <TextBlock Text="Info" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>
                    </WrapPanel>
                </StackPanel>
            </GroupBox>

            <!-- 模糊效果演示 -->
            <GroupBox Header="模糊效果 (Blur Effects)"
                      Padding="20"
                      Background="{StaticResource SurfaceBrush}">
                <StackPanel Spacing="20">

                    <!-- 基础模糊强度 -->
                    <TextBlock Text="模糊强度级别"
                               Style="{StaticResource SubheadingTextStyle}"/>

                    <WrapPanel Orientation="Horizontal">

                        <!-- 轻微模糊 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4">
                            <Border Background="LightBlue"
                                    CornerRadius="4"
                                    Effect="{StaticResource SubtleBlur}">
                                <TextBlock Text="Subtle Blur"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           TextAlignment="Center"/>
                            </Border>
                        </Border>

                        <!-- 轻度模糊 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4">
                            <Border Background="LightGreen"
                                    CornerRadius="4"
                                    Effect="{StaticResource LightBlur}">
                                <TextBlock Text="Light Blur"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           TextAlignment="Center"/>
                            </Border>
                        </Border>

                        <!-- 中度模糊 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4">
                            <Border Background="LightCoral"
                                    CornerRadius="4"
                                    Effect="{StaticResource MediumBlur}">
                                <TextBlock Text="Medium Blur"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           TextAlignment="Center"/>
                            </Border>
                        </Border>

                        <!-- 强度模糊 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4">
                            <Border Background="LightGoldenrodYellow"
                                    CornerRadius="4"
                                    Effect="{StaticResource StrongBlur}">
                                <TextBlock Text="Strong Blur"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           TextAlignment="Center"/>
                            </Border>
                        </Border>
                    </WrapPanel>

                    <!-- 亚克力模糊 -->
                    <TextBlock Text="亚克力模糊效果"
                               Style="{StaticResource SubheadingTextStyle}"
                               Margin="0,20,0,0"/>

                    <WrapPanel Orientation="Horizontal">

                        <!-- 轻度亚克力 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4">
                            <Border Background="{StaticResource LightAcrylicGradient}"
                                    CornerRadius="4"
                                    Effect="{StaticResource AcrylicLight}">
                                <TextBlock Text="Acrylic Light"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           TextAlignment="Center"/>
                            </Border>
                        </Border>

                        <!-- 中度亚克力 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4">
                            <Border Background="{StaticResource LightAcrylicGradient}"
                                    CornerRadius="4"
                                    Effect="{StaticResource AcrylicMedium}">
                                <TextBlock Text="Acrylic Medium"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           TextAlignment="Center"/>
                            </Border>
                        </Border>

                        <!-- 强度亚克力 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4">
                            <Border Background="{StaticResource LightAcrylicGradient}"
                                    CornerRadius="4"
                                    Effect="{StaticResource AcrylicStrong}">
                                <TextBlock Text="Acrylic Strong"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           TextAlignment="Center"/>
                            </Border>
                        </Border>
                    </WrapPanel>
                </StackPanel>
            </GroupBox>

            <!-- 渐变效果演示 -->
            <GroupBox Header="渐变效果 (Gradient Effects)"
                      Padding="20"
                      Background="{StaticResource SurfaceBrush}">
                <StackPanel Spacing="20">

                    <!-- 主题渐变 -->
                    <TextBlock Text="主题色渐变"
                               Style="{StaticResource SubheadingTextStyle}"/>

                    <WrapPanel Orientation="Horizontal">

                        <!-- 主色渐变 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource PrimaryLinearGradient}"
                                CornerRadius="4">
                            <TextBlock Text="Primary Gradient"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- 次要色渐变 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource SecondaryLinearGradient}"
                                CornerRadius="4">
                            <TextBlock Text="Secondary Gradient"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- 强调色渐变 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource AccentLinearGradient}"
                                CornerRadius="4">
                            <TextBlock Text="Accent Gradient"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>
                    </WrapPanel>

                    <!-- 径向渐变 -->
                    <TextBlock Text="径向渐变"
                               Style="{StaticResource SubheadingTextStyle}"
                               Margin="0,20,0,0"/>

                    <WrapPanel Orientation="Horizontal">

                        <!-- 主色径向渐变 -->
                        <Border Width="100" Height="100" Margin="10"
                                Background="{StaticResource PrimaryRadialGradient}"
                                CornerRadius="50">
                            <TextBlock Text="Primary Radial"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- 次要色径向渐变 -->
                        <Border Width="100" Height="100" Margin="10"
                                Background="{StaticResource SecondaryRadialGradient}"
                                CornerRadius="50">
                            <TextBlock Text="Secondary Radial"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>
                    </WrapPanel>

                    <!-- 语义渐变 -->
                    <TextBlock Text="语义状态渐变"
                               Style="{StaticResource SubheadingTextStyle}"
                               Margin="0,20,0,0"/>

                    <WrapPanel Orientation="Horizontal">

                        <!-- 成功渐变 -->
                        <Border Width="100" Height="60" Margin="10"
                                Background="{StaticResource SuccessGradient}"
                                CornerRadius="4">
                            <TextBlock Text="Success"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- 警告渐变 -->
                        <Border Width="100" Height="60" Margin="10"
                                Background="{StaticResource WarningGradient}"
                                CornerRadius="4">
                            <TextBlock Text="Warning"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- 错误渐变 -->
                        <Border Width="100" Height="60" Margin="10"
                                Background="{StaticResource ErrorGradient}"
                                CornerRadius="4">
                            <TextBlock Text="Error"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- 信息渐变 -->
                        <Border Width="100" Height="60" Margin="10"
                                Background="{StaticResource InfoGradient}"
                                CornerRadius="4">
                            <TextBlock Text="Info"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>
                    </WrapPanel>

                    <!-- 特殊效果渐变 -->
                    <TextBlock Text="特殊效果渐变"
                               Style="{StaticResource SubheadingTextStyle}"
                               Margin="0,20,0,0"/>

                    <WrapPanel Orientation="Horizontal">

                        <!-- 玻璃效果 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource GlassGradient}"
                                CornerRadius="4"
                                BorderBrush="{StaticResource BorderBrush}"
                                BorderThickness="1">
                            <TextBlock Text="Glass Effect"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 闪光效果 -->
                        <Border Width="120" Height="80" Margin="10"
                                Background="{StaticResource ShimmerGradient}"
                                CornerRadius="4">
                            <TextBlock Text="Shimmer Effect"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>
                    </WrapPanel>
                </StackPanel>
            </GroupBox>

            <!-- 组合效果演示 -->
            <GroupBox Header="组合效果样式 (Combined Effect Styles)"
                      Padding="20"
                      Background="{StaticResource SurfaceBrush}">
                <StackPanel Spacing="20">

                    <!-- 预定义组合样式 -->
                    <TextBlock Text="预定义组合样式"
                               Style="{StaticResource SubheadingTextStyle}"/>

                    <WrapPanel Orientation="Horizontal">

                        <!-- 卡片效果样式 -->
                        <Border Width="140" Height="100" Margin="10"
                                Style="{StaticResource CardEffectStyle}"
                                CornerRadius="8">
                            <TextBlock Text="Card Effect Style"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"
                                       Margin="10"/>
                        </Border>

                        <!-- 按钮效果样式 -->
                        <Button Width="140" Height="50" Margin="10"
                                Content="Button Effect Style"
                                Style="{StaticResource ButtonEffectStyle}"/>

                        <!-- 浮动按钮效果样式 -->
                        <Button Width="80" Height="80" Margin="10"
                                Content="FAB"
                                Style="{StaticResource FloatingButtonEffectStyle}"
                                Template="{DynamicResource {x:Static ToolBar.ButtonStyleKey}}"/>
                    </WrapPanel>

                    <!-- 语义状态效果样式 -->
                    <TextBlock Text="语义状态效果样式"
                               Style="{StaticResource SubheadingTextStyle}"
                               Margin="0,20,0,0"/>

                    <WrapPanel Orientation="Horizontal">

                        <!-- 成功状态效果 -->
                        <Border Width="120" Height="60" Margin="10"
                                Style="{StaticResource SuccessEffectStyle}"
                                CornerRadius="4">
                            <TextBlock Text="Success Style"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- 警告状态效果 -->
                        <Border Width="120" Height="60" Margin="10"
                                Style="{StaticResource WarningEffectStyle}"
                                CornerRadius="4">
                            <TextBlock Text="Warning Style"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>

                        <!-- 错误状态效果 -->
                        <Border Width="120" Height="60" Margin="10"
                                Style="{StaticResource ErrorEffectStyle}"
                                CornerRadius="4">
                            <TextBlock Text="Error Style"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>
                    </WrapPanel>
                </StackPanel>
            </GroupBox>

            <!-- 附加属性演示 -->
            <GroupBox Header="附加属性演示 (Attached Properties)"
                      Padding="20"
                      Background="{StaticResource SurfaceBrush}">
                <StackPanel Spacing="20">

                    <TextBlock Text="使用附加属性动态设置效果"
                               Style="{StaticResource SubheadingTextStyle}"/>

                    <WrapPanel Orientation="Horizontal">

                        <!-- Elevation 附加属性 -->
                        <Border Width="100" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                effects:EffectAttachedProperties.Elevation="6">
                            <TextBlock Text="Elevation=6"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 发光附加属性 -->
                        <Border Width="100" Height="80" Margin="10"
                                Background="{StaticResource CardBackgroundBrush}"
                                CornerRadius="4"
                                effects:EffectAttachedProperties.GlowColor="Blue"
                                effects:EffectAttachedProperties.GlowIntensity="0.6"
                                effects:EffectAttachedProperties.GlowRadius="12">
                            <TextBlock Text="Custom Glow"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>

                        <!-- 模糊附加属性 -->
                        <Border Width="100" Height="80" Margin="10"
                                Background="LightPink"
                                CornerRadius="4"
                                effects:EffectAttachedProperties.BlurRadius="6">
                            <TextBlock Text="Blur Radius=6"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       TextAlignment="Center"/>
                        </Border>
                    </WrapPanel>
                </StackPanel>
            </GroupBox>

        </StackPanel>
    </ScrollViewer>
</Page>
