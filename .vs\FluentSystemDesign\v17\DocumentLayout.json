{"Version": 1, "WorkspaceRootPath": "D:\\Project\\05 FluentDesignWPF\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{153730A0-80A3-484A-A8E8-371988079EEF}|FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj|d:\\project\\05 fluentdesignwpf\\fluentsystemdesign.examples\\pages\\examples\\effectsdemopage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{153730A0-80A3-484A-A8E8-371988079EEF}|FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj|solutionrelative:fluentsystemdesign.examples\\pages\\examples\\effectsdemopage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{153730A0-80A3-484A-A8E8-371988079EEF}|FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj|d:\\project\\05 fluentdesignwpf\\fluentsystemdesign.examples\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{153730A0-80A3-484A-A8E8-371988079EEF}|FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj|solutionrelative:fluentsystemdesign.examples\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{153730A0-80A3-484A-A8E8-371988079EEF}|FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj|d:\\project\\05 fluentdesignwpf\\fluentsystemdesign.examples\\pages\\homepage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{153730A0-80A3-484A-A8E8-371988079EEF}|FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj|solutionrelative:fluentsystemdesign.examples\\pages\\homepage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{153730A0-80A3-484A-A8E8-371988079EEF}|FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj|d:\\project\\05 fluentdesignwpf\\fluentsystemdesign.examples\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{153730A0-80A3-484A-A8E8-371988079EEF}|FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj|solutionrelative:fluentsystemdesign.examples\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{153730A0-80A3-484A-A8E8-371988079EEF}|FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj|d:\\project\\05 fluentdesignwpf\\fluentsystemdesign.examples\\pages\\examples\\behaviorsdemopage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{153730A0-80A3-484A-A8E8-371988079EEF}|FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj|solutionrelative:fluentsystemdesign.examples\\pages\\examples\\behaviorsdemopage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "HomePage.xaml", "DocumentMoniker": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\Pages\\HomePage.xaml", "RelativeDocumentMoniker": "FluentSystemDesign.Examples\\Pages\\HomePage.xaml", "ToolTip": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\Pages\\HomePage.xaml", "RelativeToolTip": "FluentSystemDesign.Examples\\Pages\\HomePage.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T09:06:24.829Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "App.xaml", "DocumentMoniker": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\App.xaml", "RelativeDocumentMoniker": "FluentSystemDesign.Examples\\App.xaml", "ToolTip": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\App.xaml", "RelativeToolTip": "FluentSystemDesign.Examples\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T09:06:23.566Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "EffectsDemoPage.xaml", "DocumentMoniker": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\Pages\\Examples\\EffectsDemoPage.xaml", "RelativeDocumentMoniker": "FluentSystemDesign.Examples\\Pages\\Examples\\EffectsDemoPage.xaml", "ToolTip": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\Pages\\Examples\\EffectsDemoPage.xaml", "RelativeToolTip": "FluentSystemDesign.Examples\\Pages\\Examples\\EffectsDemoPage.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T09:06:02.871Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "BehaviorsDemoPage.xaml", "DocumentMoniker": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\Pages\\Examples\\BehaviorsDemoPage.xaml", "RelativeDocumentMoniker": "FluentSystemDesign.Examples\\Pages\\Examples\\BehaviorsDemoPage.xaml", "ToolTip": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\Pages\\Examples\\BehaviorsDemoPage.xaml", "RelativeToolTip": "FluentSystemDesign.Examples\\Pages\\Examples\\BehaviorsDemoPage.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T07:42:10.233Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\MainWindow.xaml", "RelativeDocumentMoniker": "FluentSystemDesign.Examples\\MainWindow.xaml", "ToolTip": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\MainWindow.xaml", "RelativeToolTip": "FluentSystemDesign.Examples\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T07:19:11.366Z", "EditorCaption": ""}]}]}]}